/* Workflow Step Animations */

/* Step transition animations */
.step-enter {
  opacity: 0;
  transform: translateX(30px);
}

.step-enter-active {
  opacity: 1;
  transform: translateX(0);
  transition: opacity 500ms ease-out, transform 500ms ease-out;
}

.step-exit {
  opacity: 1;
  transform: translateX(0);
}

.step-exit-active {
  opacity: 0;
  transform: translateX(-30px);
  transition: opacity 300ms ease-in, transform 300ms ease-in;
}

/* Progress bar animations */
.progress-fill {
  transition: width 700ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* Step circle animations */
.step-circle {
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

.step-circle:hover {
  transform: scale(1.1);
}

.step-circle.completed {
  animation: completePulse 0.6s ease-out;
}

.step-circle.current {
  animation: currentPulse 2s ease-in-out infinite;
}

/* Keyframe animations */
@keyframes completePulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.7);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 0 0 10px rgba(34, 197, 94, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0);
  }
}

@keyframes currentPulse {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(232, 196, 160, 0.4);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(232, 196, 160, 0);
  }
}

/* Skeleton loading animations */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Smooth transitions for content changes */
.content-transition {
  transition: all 500ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* Fade in animation for new content */
.fade-in {
  animation: fadeIn 500ms ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Success animation */
.success-bounce {
  animation: successBounce 0.8s ease-out;
}

@keyframes successBounce {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Loading spinner improvements */
.spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Hover effects */
.hover-lift {
  transition: transform 200ms ease-out, box-shadow 200ms ease-out;
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}
