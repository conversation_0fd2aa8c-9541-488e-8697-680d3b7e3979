# Project-wide .gitignore for Coffee Meetings Platform

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Django
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal
media/
staticfiles/
static/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
backend/.env

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/
.venv/
backend/venv/

# IDE
.idea/
.vscode/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Node.js (si vous ajoutez un frontend)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Frontend build files
frontend/build/
frontend/dist/
frontend/.next/
frontend/out/

# Cache
.pytest_cache/
.coverage
htmlcov/
.cache/

# Logs
*.log
logs/

# Temporary files
*.tmp
*.temp

# Database
*.db
*.sqlite
*.sqlite3

# Backup files
*.bak
*.backup

# Test files (optionnel - décommentez si vous ne voulez pas versionner les tests)
# test_*.py
# *_test.py

# Documentation builds
docs/_build/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# pipenv
Pipfile.lock

# Poetry
poetry.lock

# Migrations (optionnel - décommentez si vous ne voulez pas versionner les migrations)
# backend/*/migrations/
# !backend/*/migrations/__init__.py

# Specific to this project
backend/test_*.py
backend/staticfiles/
backend/media/

# Email templates cache (si vous en ajoutez)
backend/templates/compiled/

# SSL certificates
*.pem
*.key
*.crt

# Docker
.dockerignore
docker-compose.override.yml

# Deployment
.env.production
.env.staging

# Local configuration
local_settings.py
settings_local.py

.config/
